<template>
  <div class="h-full flex flex-col">
    <slot name="before" />

    <div v-if="sheetMixin.hasLayout('search')" class="flex-none">
      <EleSheetSearch
        v-show="toolbarMixin.searchVisible"
        ref="searchRef"
        v-bind="{
          ...commonProps('search'),
          searchMixin,
          load: getTableData,
          ...searchProps,
        }"
        v-on="$listeners"
      >
        <template
          v-for="(item, index) of sheetMixin.getSlotModel('search', '<field-value>', 'before')"
          #[sheetMixin.separatorJoin(item.field,`before`)]="scope"
        >
          <slot :name="sheetMixin.separatorJoin('search', item.field, 'before')" v-bind="scope" />
        </template>

        <template
          v-for="(item, index) of sheetMixin.getSlotModel('search', '<field-value>', 'simple')"
          #[sheetMixin.separatorJoin(item.field,`simple`)]="scope"
        >
          <slot :name="sheetMixin.separatorJoin('search', item.field, 'simple')" v-bind="scope" />
        </template>

        <template v-for="(item, index) of sheetMixin.getSlotModel('search')" #[item.field]="scope">
          <slot :name="sheetMixin.separatorJoin('search', item.field)" v-bind="scope" />
        </template>

        <template
          v-for="(item, index) of sheetMixin.getSlotModel('search', '<field-value>', 'after')"
          #[sheetMixin.separatorJoin(item.field,`after`)]="scope"
        >
          <slot :name="sheetMixin.separatorJoin('search', item.field, 'after')" v-bind="scope" />
        </template>
      </EleSheetSearch>
    </div>

    <slot name="search-to-toolbar" />

    <div v-if="sheetMixin.hasLayout('toolbar')" class="flex-none mb-4">
      <EleSheetToolbar
        ref="toolbarRef"
        v-bind="{
          ...commonProps('toolbar'),
          toolbarMixin,
          ...toolbarProps,
          layout:
            toolbarProps.layout ||
            filterObjectKeys({ refresh: true, search: sheetMixin.hasLayout('search') }),
        }"
      >
        <slot name="toolbar" v-bind="{ ...toolbarSlotProps }">
          <slot name="toolbar:before" v-bind="{ ...toolbarSlotProps }" />

          <el-button v-if="sheetMixin.hasAction('add')" type="primary" @click="handleAdd">
            新增
          </el-button>
          <el-button v-if="sheetMixin.hasAction('import')" type="default" @click="handleImport">
            导入
          </el-button>
          <el-button v-if="sheetMixin.hasAction('export')" type="default" @click="handleExport">
            导出
          </el-button>
          <el-button
            v-if="sheetMixin.hasAction('remove')"
            type="danger"
            :disabled="!tableMixin.selected"
            @click="handleRemove()"
          >删除</el-button>

          <slot name="toolbar:after" v-bind="{ ...toolbarSlotProps }" />
        </slot>
      </EleSheetToolbar>
    </div>

    <slot name="toolbar-to-table" />

    <div v-if="sheetMixin.hasLayout('table')" class="flex-1 h-0">
      <EleSheetTable
        ref="tableRef"
        v-bind="{
          ...commonProps('table'),
          ...tableProps,
          tableColumnProps,
          tableMixin,
          searchMixin,
          pagingMixin,
        }"
        v-on="$listeners"
      >
        <template #empty>
          <slot :name="sheetMixin.separatorJoin('table', 'empty')" />
        </template>

        <template #before="scope">
          <slot :name="sheetMixin.separatorJoin('table', 'before')" v-bind="scope" />
        </template>

        <template
          v-for="(item, index) of sheetMixin.getSlotModel('table', '<field-value>', 'before')"
          #[sheetMixin.separatorJoin(item.field,`before`)]="scope"
        >
          <slot :name="sheetMixin.separatorJoin('table', item.field, 'before')" v-bind="scope" />
        </template>

        <template
          v-for="(item, index) of sheetMixin.getSlotModel('table', '<field-value>', 'simple')"
          #[sheetMixin.separatorJoin(item.field,`simple`)]="scope"
        >
          <slot :name="sheetMixin.separatorJoin('table', item.field, 'simple')" v-bind="scope" />
        </template>

        <template v-for="(item, index) of sheetMixin.getSlotModel('table')" #[item.field]="scope">
          <slot :name="sheetMixin.separatorJoin('table', item.field)" v-bind="scope" />
        </template>

        <template
          v-for="(item, index) of sheetMixin.getSlotModel('table', '<field-value>', 'after')"
          #[sheetMixin.separatorJoin(item.field,`after`)]="scope"
        >
          <slot :name="sheetMixin.separatorJoin('table', item.field, 'after')" v-bind="scope" />
        </template>

        <template #after="scope">
          <slot
            :name="sheetMixin.separatorJoin('table', 'after')"
            v-bind="{
              ...scope,
              infoHandler: handleInfo,
              editHandler: handleEdit,
              removeHandler: handleRemove,
            }"
          >
            <el-table-column
              v-if="
                ['info', 'edit', 'remove'].some((item) => api[item]) && !sheetMixin.hasAction('all')
              "
              v-bind="{
                fixed: 'right',
                width: 200,
                align: 'center',
                label: '操作',
                ...tableActionProps,
              }"
            >
              <template #default="{ row, $index }">
                <slot
                  :name="sheetMixin.separatorJoin('table', 'action', 'before')"
                  v-bind="{ row, $index }"
                />

                <el-button
                  v-if="sheetMixin.hasAction('info')"
                  size="mini"
                  type="text"
                  @click="handleInfo(row)"
                >查看</el-button>
                <el-button
                  v-if="sheetMixin.hasAction('edit')"
                  size="mini"
                  type="text"
                  @click="handleEdit(row)"
                >编辑</el-button>
                <el-button
                  v-if="sheetMixin.hasAction('remove')"
                  size="mini"
                  type="text"
                  @click="handleRemove(row)"
                >删除</el-button>

                <slot
                  :name="sheetMixin.separatorJoin('table', 'action', 'after')"
                  v-bind="{ row, $index }"
                />
              </template>
            </el-table-column>
          </slot>
        </template>
      </EleSheetTable>
    </div>

    <slot name="table-to-paging" />

    <div v-if="sheetMixin.hasLayout('paging')" class="flex-none mt-4">
      <EleSheetPaging
        ref="pagingRef"
        v-bind="{
          ...commonProps('paging'),
          pagingMixin,
          ...pagingProps,
          layout: pagingProps.layout,
        }"
        v-on="$listeners"
        @change="onPagingChange"
      />
    </div>

    <slot name="after" />

    <component
      :is="action.component"
      v-for="(action, actionType) of formModel"
      :key="actionType"
      :ref="action.ref"
      v-bind="{ ...commonProps(action.scope), formProps: action.props, actionType }"
    >
      <template #before="scope">
        <slot :name="sheetMixin.separatorJoin(actionType, 'before')" v-bind="scope" />
      </template>

      <template
        v-for="(item, index) of sheetMixin.getSlotModel(action.scope, '<field-value>', 'before')"
        #[sheetMixin.separatorJoin(item.field,`before`)]="scope"
      >
        <slot :name="sheetMixin.separatorJoin(action.scope, item.field, 'before')" v-bind="scope" />
      </template>

      <template
        v-for="(item, index) of sheetMixin.getSlotModel(action.scope, '<field-value>', 'simple')"
        #[sheetMixin.separatorJoin(item.field,`simple`)]="scope"
      >
        <slot :name="sheetMixin.separatorJoin(action.scope, item.field, 'simple')" v-bind="scope" />
      </template>

      <template
        v-for="(item, index) of sheetMixin.getSlotModel(action.scope)"
        #[item.field]="scope"
      >
        <slot :name="sheetMixin.separatorJoin(action.scope, item.field)" v-bind="scope" />
      </template>

      <template
        v-for="(item, index) of sheetMixin.getSlotModel(action.scope, '<field-value>', 'after')"
        #[sheetMixin.separatorJoin(item.field,`after`)]="scope"
      >
        <slot :name="sheetMixin.separatorJoin(action.scope, item.field, 'after')" v-bind="scope" />
      </template>

      <!-- 更加细分的插槽 -->
      <template
        v-for="(item, index) of sheetMixin.getSlotModel(actionType, '<field-value>', 'before')"
        #[sheetMixin.separatorJoin(item.field,actionType,`before`)]="scope"
      >
        <slot :name="sheetMixin.separatorJoin(actionType, item.field, 'before')" v-bind="scope" />
      </template>

      <template
        v-for="(item, index) of sheetMixin.getSlotModel(actionType, '<field-value>', 'simple')"
        #[sheetMixin.separatorJoin(item.field,actionType,`simple`)]="scope"
      >
        <slot :name="sheetMixin.separatorJoin(actionType, item.field, 'simple')" v-bind="scope" />
      </template>

      <template
        v-for="(item, index) of sheetMixin.getSlotModel(actionType)"
        #[sheetMixin.separatorJoin(item.field,actionType)]="scope"
      >
        <slot :name="sheetMixin.separatorJoin(actionType, item.field)" v-bind="scope" />
      </template>

      <template
        v-for="(item, index) of sheetMixin.getSlotModel(actionType, '<field-value>', 'after')"
        #[sheetMixin.separatorJoin(item.field,actionType,`after`)]="scope"
      >
        <slot :name="sheetMixin.separatorJoin(actionType, item.field, 'after')" v-bind="scope" />
      </template>

      <template #after="scope">
        <slot :name="sheetMixin.separatorJoin(actionType, 'after')" v-bind="scope" />
      </template>
    </component>

    <EleSheetImport ref="importRef" v-bind="{ ...importProps }" v-on="$listeners" />
  </div>
</template>

<script>
import { pick } from 'lodash-es'

import {
  sheetMixin,
  searchMixin,
  tableMixin,
  toolbarMixin,
  pagingMixin
} from '@/plugins/element-extends/mixins/index.js'

import { download } from '@/utils/request'

import { inheritComponentMethods, filterObjectKeys } from '@/plugins/element-extends/helper.js'

export default {
  name: 'EleSheet',
  mixins: [
    sheetMixin(),
    searchMixin(),
    toolbarMixin(),
    tableMixin({ refKey: '$refs.tableRef.$refs.tableRef' }),
    pagingMixin()
  ],
  props: {
    title: {
      type: String,
      default: ''
    },
    layout: {
      type: [String, Array],
      default: 'search,toolbar,table,paging'
    },
    searchProps: {
      type: Object,
      default: () => ({})
    },
    tableProps: {
      type: Object,
      default: () => ({})
    },
    tableColumnProps: {
      type: Object,
      default: () => ({})
    },
    toolbarProps: {
      type: Object,
      default: () => ({})
    },
    pagingProps: {
      type: Object,
      default: () => ({})
    },
    formProps: {
      type: Object,
      default: () => ({})
    },
    infoProps: {
      type: Object,
      default: () => ({})
    },
    importProps: {
      type: Object,
      default: () => ({})
    },
    defaultPageSize: {
      type: Number,
      default: 10
    },
    tableActionProps: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {}
  },
  computed: {
    formModel() {
      const value = {
        add: {
          component: 'EleSheetForm',
          scope: 'form',
          ref: 'addFormRef',
          props: {
            ...this.formProps
          }
        },
        edit: {
          component: 'EleSheetForm',
          scope: 'form',
          ref: 'editFormRef',
          props: {
            ...this.formProps
          }
        },
        info: {
          component: 'EleSheetInfo',
          scope: 'info',
          ref: 'infoFormRef',
          props: {
            ...this.infoProps
          }
        }
      }

      return value
    },
    toolbarSlotProps() {
      return {
        ...this.tableMixin,
        addHandler: this.handleAdd,
        importHandler: this.handleImport,
        exportHandler: this.handleExport,
        removeHandler: this.handleRemove
      }
    }
  },
  created() {
    this.pagingMixin.pageSize = this.defaultPageSize
  },
  methods: {
    filterObjectKeys,
    ...inheritComponentMethods('tableRef', ['getTableData']),

    listParameter() {
      const value = {
        ...this.searchMixin.parameter(),
        ...this.pagingMixin.parameter()
      }

      return value
    },

    onPagingChange(event) {
      this.$emit('paging-change', event)
      this.getTableData()
    },

    commonProps() {
      const value = {
        ...pick(this.$props, Object.keys(sheetMixin().props))
      }

      return value
    },

    handleAdd() {
      const addFormRef = this.$refs.addFormRef[0]

      addFormRef.open({
        title: `新增${this.title}`,
        success: (event) => {
          this.$emit('add-success', event)
          this.getTableData()
        }
      })
    },

    handleEdit(row) {
      const editFormRef = this.$refs.editFormRef[0]

      editFormRef.open({
        title: `编辑${this.title}`,
        ...row,
        success: (event) => {
          this.$emit('edit-success', event)
          this.getTableData()
        }
      })
    },

    handleInfo(row) {
      const infoFormRef = this.$refs.infoFormRef[0]

      infoFormRef.open({
        title: `${this.title}详情`,
        ...row
      })
    },

    async handleRemove(row) {
      try {
        await this.$confirm('是否确认删除当前所选数据项？', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
      } catch (error) {
        console.warn(error?.message || error)
        return false
      }

      const params = row?.id ? row?.id : this.tableMixin.selectionIds

      this.tableMixin.loading = true

      let res
      let catchError

      try {
        res = await this.api.remove(params, row)
      } catch (error) {
        catchError = error
      }

      this.tableMixin.loading = false

      if (catchError) {
        console.log(catchError?.message || catchError)
        return false
      }

      this.$message.success(res.msg)
      this.$emit('remove-success', res)
      this.getTableData()
    },
    getApiUrl(type, ...args) {
      const api = this.api[type]
      if (!api) {
        return ''
      }

      if (typeof api === 'function') {
        return api(...args)
      }

      return api
    },
    async handleImport() {
      const action = await this.getApiUrl('import')
      const template = await this.getApiUrl('template')

      this.$refs.importRef.open({
        title: `${this.title}列表`,
        action,
        template,
        success: (event) => {
          this.$emit('import-success', event)
          this.getTableData()
        }
      })
    },

    async handleExport() {
      const params = {
        ...this.pagingMixin.parameter(),
        ...this.searchMixin.parameter()
      }

      const handler = (url, args = {}) => {
        const fileName = args.fileName || `${this.title}_${new Date().getTime()}.xlsx`

        const query = args.parameter ? args.parameter(params) : params

        return download(url, query, fileName)
      }

      if (typeof this.api.export === 'string') {
        await handler(this.api.export)
      } else {
        await this.api.export(handler)
      }

      this.$emit('export-success')
    }
  }
}
</script>

<style></style>
