<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
    <template #before></template>

    <!-- 工具栏自定义按钮 -->
    <template #toolbar:after>
      <!-- <el-button type="primary" size="small" @click="handleCreateTask">创建新任务</el-button> -->
    </template>

    <!-- 状态列自定义渲染 -->
    <template #table:value10:simple="{ row }">
      <el-tag :type="getStatusTagType(row.value10)">
        {{ row.value10 }}
      </el-tag>
    </template>

    <!-- 采集协议列自定义渲染 -->
    <template #table:value4:simple="{ row }">
      <el-tag type="info">
        {{ row.value4 }}
      </el-tag>
    </template>

    <!-- 操作列自定义渲染 -->
    <template #table:action:after="{ row }">
      <el-button type="text" size="mini" @click="handleDistribute(row)">分发</el-button>
    </template>

    <template #info:before></template>
    <template #after></template>
  </EleSheet>
</template>

<script>
import request from '@/utils/request.js'
import { taskStatus, collectionSourceType, collectionProtocol, videoCodecFormat, resolutionOptions, scheduleStrategy } from '@/dicts/video/index.js'

export default {
  name: 'VideoFileManagement',
  data() {
    return {
      tableType: 'scheduled_task_queue'
    }
  },
  computed: {
    sheetProps() {
      return {
        title: '视频文件管理',
        api: {
          list: (params) =>
            request({
              url: '/system/AutoOsmotic/list',
              method: 'get',
              params: {
                ...params,
                type: this.tableType
              }
            }),
          info: (id) =>
            request({
              url: `/system/AutoOsmotic/${id}`,
              method: 'get'
            }),
          add: (data) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'post',
              data: {
                ...data,
                type: this.tableType
              }
            }),
          edit: (data) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'put',
              data: {
                ...data,
                type: this.tableType
              }
            }),
          remove: (ids) =>
            request({
              url: `/system/AutoOsmotic/${ids}`,
              method: 'delete'
            })
        },

        model: {
          // 搜索字段
          value1: {
            type: 'text',
            label: '任务ID',
            align: 'left',
            width: 120,
            search: {
              hidden: true
            }
          },
          value2: {
            type: 'text',
            label: '任务名称',
            align: 'left',
            width: 200,
            search: {
              placeholder: '请输入任务名称'
            }
          },
          value3: {
            type: 'text',
            label: '任务编号',
            width: 150,
            search: {
              hidden: true
            }
          },
          value4: {
            type: 'select',
            label: '采集协议',
            width: 120,
            search: {
              hidden: true
            },
            options: collectionProtocol
          },
          value5: {
            type: 'text',
            label: '视频源配置',
            width: 200,
            search: {
              hidden: true
            }
          },
          value6: {
            type: 'text',
            label: '视频源描述',
            width: 200,
            search: {
              hidden: true
            }
          },
          value7: {
            type: 'datetime',
            label: '采集开始时间',
            width: 160,
            search: {
              hidden: true
            }
          },
          value8: {
            type: 'datetime',
            label: '采集结束时间',
            width: 160,
            search: {
              hidden: true
            }
          },
          value9: {
            type: 'text',
            label: '采集帧率',
            width: 100,
            search: {
              hidden: true
            }
          },
          value10: {
            type: 'select',
            label: '状态',
            width: 100,
            search: {
              type: 'select',
              options: [
                { label: '全部状态', value: '' },
                ...taskStatus
              ]
            },
            options: taskStatus
          },
          value11: {
            type: 'select',
            label: '调度策略',
            width: 120,
            search: {
              hidden: true
            },
            options: scheduleStrategy
          },
          value12: {
            type: 'text',
            label: '异常重试',
            width: 100,
            search: {
              hidden: true
            }
          },
          value13: {
            type: 'text',
            label: '任务失败原因',
            width: 200,
            search: {
              hidden: true
            }
          },
          value14: {
            type: 'select',
            label: '分辨率',
            width: 120,
            search: {
              hidden: true
            },
            options: resolutionOptions
          },
          value15: {
            type: 'select',
            label: '视频编码格式',
            width: 140,
            search: {
              hidden: true
            },
            options: videoCodecFormat
          },
          value16: {
            type: 'text',
            label: '采集日志ID',
            width: 120,
            search: {
              hidden: true
            }
          },
          value17: {
            type: 'datetime',
            label: '创建时间',
            width: 160,
            search: {
              hidden: true
            }
          },
          value18: {
            type: 'datetime',
            label: '最后更新时间',
            width: 160,
            search: {
              hidden: true
            }
          },
          value19: {
            type: 'text',
            label: '创建人',
            width: 120,
            search: {
              hidden: true
            }
          },
          // 搜索专用字段
          searchTaskStatus: {
            type: 'select',
            label: '任务状态',
            width: 120,
            search: {
              type: 'select',
              options: [
                { label: '全部状态', value: '' },
                ...taskStatus
              ]
            },
            table: {
              hidden: true
            }
          },
          searchCollectionSource: {
            type: 'select',
            label: '采集源',
            width: 120,
            search: {
              type: 'select',
              options: [
                { label: '全部来源', value: '' },
                ...collectionSourceType
              ]
            },
            table: {
              hidden: true
            }
          }
        }
      }
    }
  },
  methods: {
    // 获取状态标签类型
    getStatusTagType(status) {
      const statusMap = {
        '运行中': 'success',
        '已停止': 'info',
        '异常': 'danger',
        '待执行': 'warning'
      }
      return statusMap[status] || 'info'
    },

    // 创建新任务
    handleCreateTask() {
      this.$refs.sheetRef.handleAdd()
    },

    // 分发任务
    handleDistribute(row) {
      this.$modal.confirm(`确认要分发任务"${row.value2}"吗？`).then(() => {
        // 这里可以调用分发接口
        console.log('分发任务:', row)
        this.$modal.msgSuccess('分发成功')
        // 刷新列表
        this.$refs.sheetRef.getTableData()
      }).catch(() => {})
    }
  }
}
</script>

<style scoped>
.page-main {
  height: 100%;
}
</style>
